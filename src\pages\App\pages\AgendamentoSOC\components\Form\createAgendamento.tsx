/* eslint-disable react-hooks/exhaustive-deps */
import { Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  InputDateBox,
  InputTextBox,
  SelectBox,
  useToast,
} from '@onyma-ds/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { format, getDay, isAfter, isBefore, parse } from 'date-fns';
import { useEffect, useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ptBR } from 'date-fns/locale';
import * as SC from './styles';
import { AgendamentoSchema, AgendamentoSchemaType } from './validations';
import { LabelAndValue } from '@/@types/LabelAndValue';

export default function FormCreate() {
  const navigate = useNavigate();

  const { addToast } = useToast();
  const { user } = useAuth();
  const {
    calendar: { socCreateSchedule, loadCalendar, loadAvailableDates },
    clients: { loadClientCompany },
    menus: { loadMenu },
  } = useApi();
  const [searchParams] = useSearchParams();
  const menuId = searchParams.get('menuId');

  const [availableDates, setAvailableDates] = useState<Date[]>([]);
  const [availableHours, setAvailableHours] = useState<
    { label: string; value: string }[]
  >([]);

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<AgendamentoSchemaType>({
    resolver: zodResolver(AgendamentoSchema),
    defaultValues: {
      matriculaColaborador: user.socRegister,
      nomeColaborador: user.fullName,
      emailPessoal: user.email,
    },
  });

  const selectedDate = useWatch({ control, name: 'dataAgendamento' });

  const { data: currentMenuData } = useQuery({
    queryKey: ['loadMenu'],
    queryFn: () => loadMenu({ id: String(menuId) }),
    refetchOnWindowFocus: false,
  });

  const { data: currentAgendaData } = useQuery({
    queryKey: ['agenda', currentMenuData],
    queryFn: () => loadCalendar(Number(currentMenuData?.result.agendaId)),
    refetchOnWindowFocus: false,
  });

  const { data: currentCompanyData } = useQuery({
    queryKey: ['currentCompany'],
    queryFn: () => loadClientCompany(user.companyId as string),
    refetchOnWindowFocus: false,
  });

  const { data: availableDatesData, isLoading: isLoadingAvailableDates } =
    useQuery({
      queryKey: ['availableDates', currentAgendaData],
      queryFn: () =>
        loadAvailableDates({
          codigoAgenda: currentAgendaData?.result.codigo as string,
          codigoSoc: currentCompanyData?.result.codigoSoc as string,
          haveToFetchSixtyDays: currentAgendaData?.result.codigo === '2287920', // agenda elopar para buscar 60 dias
        }),
      refetchOnWindowFocus: false,
    });

  const { mutate, isPending } = useMutation({
    mutationFn: (formData: AgendamentoSchemaType) =>
      socCreateSchedule({
        dataAgendamento: formData.dataAgendamento.toLocaleDateString(
          'pt-BR',
        ) as string,
        matricula: formData.matriculaColaborador,
        nomeFuncionario: formData.nomeColaborador,
        emailPessoal: formData.emailPessoal,
        horaInicio: formData.hour.value as string,
        email: currentAgendaData?.result.email as string,
        nomeAgenda: currentAgendaData?.result?.nome as string,
        idTipoExame: currentAgendaData?.result.tipoExame as string,
        enderecoAgenda: currentAgendaData?.result?.endereco as string,
        telefoneAgenda: currentAgendaData?.result?.telefone as string,
        codigoUsuarioAgenda: currentAgendaData?.result.codigo as string,
        idEmpresaCliente: user.companyId as string,
        codigoFuncionario: user.socCode,
        codigoEmpresa: currentCompanyData?.result?.codigoSoc as string,
        idTipoCompromisso: currentMenuData?.result.tipoCompromisso
          ?.id as string,
        detalhes: '',
        textoLivre: '',
        tipoEmail: 'AGENDAMENTO',
      }),
    onSuccess: (response) => {
      addToast({
        type: 'success',
        title: response.title,
        description: response.message,
        timeout: 5000,
      });
    },
    onError: (error) => {
      addToast({
        type: 'error',
        title: 'Erro realizar agendamento, por favor, tente novamente',
        description: error.message,
        timeout: 5000,
      });
    },
  });

  const handleSubmitForm = (data: AgendamentoSchemaType) => {
    mutate(data);
  };

  useEffect(() => {
    if (!availableDatesData || !currentAgendaData) return;

    const availableDays = currentAgendaData.result.disponibilidade;

    const validDaysOfWeek = Object.entries(availableDays)
      .filter(([, value]) => value !== null)
      .map(([day]) => day);

    const daysOfWeekMap = {
      domingo: 0,
      segunda: 1,
      terca: 2,
      quarta: 3,
      quinta: 4,
      sexta: 5,
      sabado: 6,
    };
    const filteredDates = availableDatesData.result
      .map((item) => item.dateTyped)
      .filter((date) => {
        const currentDayName = format(date, 'ccc', {
          locale: ptBR,
        }).replace('ç', 'c') as
          | 'domingo'
          | 'segunda'
          | 'terca'
          | 'quarta'
          | 'quinta'
          | 'sexta'
          | 'sabado';
        const dayOfWeek = daysOfWeekMap[currentDayName];

        return validDaysOfWeek.includes(Object.keys(daysOfWeekMap)[dayOfWeek]);
      });

    setAvailableDates(filteredDates);
  }, [availableDatesData, currentAgendaData]);

  useEffect(() => {
    if (!selectedDate || !availableDatesData || !currentAgendaData) {
      setAvailableHours([]);
      return;
    }

    const selectedDateString = format(selectedDate, 'dd/MM/yyyy');
    const selectedDayIndex = getDay(selectedDate);
    const daysOfWeek = [
      'domingo',
      'segunda',
      'terca',
      'quarta',
      'quinta',
      'sexta',
      'sabado',
    ];
    const selectedDay = daysOfWeek[selectedDayIndex];

    const availableTimes =
      currentAgendaData.result.disponibilidade[selectedDay] || [];

    const horarios = availableDatesData.result
      .filter((item) => item.data === selectedDateString)
      .flatMap((item) => {
        const hour = parse(item.horario, 'HH:mm', new Date());

        const isAvailable = availableTimes.some((timeRange) => {
          const [start, end] = timeRange
            .split('-')
            .map((time) => parse(time.trim(), 'HH:mm', new Date()));

          return isAfter(hour, start) && isBefore(hour, end);
        });

        return isAvailable
          ? { label: item.horario, value: item.horario }
          : null;
      })
      .filter(Boolean);

    setAvailableHours(horarios as LabelAndValue[]);
  }, [selectedDate, availableDatesData, currentAgendaData]);

  useEffect(() => {
    if (currentMenuData?.result && !currentMenuData?.result.agendaId) {
      addToast({
        type: 'error',
        title: 'Erro ao carregar dados da agenda',
        description: 'Não foi possível carregar os dados da agenda',
        timeout: 5000,
      });
      navigate(-1); // back to previous page
    }
  }, [currentMenuData]);

  return (
    <SC.Container
      noValidate
      onSubmit={handleSubmit(handleSubmitForm)}
    >
      <InputTextBox
        label="Empresa atual (o agendamento será realizada para essa empresa)"
        placeholder="Selecione uma empresa"
        disabled
        value={user.companyName as string}
      />
      <Controller
        name="matriculaColaborador"
        control={control}
        render={({ field }) => (
          <InputTextBox
            label="Matrícula do colaborador"
            defaultValue={user.socRegister}
            value={field.value}
            onChange={field.onChange}
            error={!!errors.matriculaColaborador}
            feedbackText={errors.matriculaColaborador?.message}
          />
        )}
      />

      <Controller
        name="nomeColaborador"
        control={control}
        render={({ field }) => (
          <InputTextBox
            label="Nome do colaborador"
            defaultValue={user.fullName}
            {...register('nomeColaborador')}
            value={field.value}
            onChange={field.onChange}
            error={!!errors.nomeColaborador}
            feedbackText={errors.nomeColaborador?.message}
          />
        )}
      />

      <Controller
        name="emailPessoal"
        control={control}
        render={({ field }) => (
          <InputTextBox
            type="email"
            label="E-mail pessoal"
            defaultValue={user.email}
            value={field.value}
            onChange={field.onChange}
            error={!!errors.emailPessoal}
            feedbackText={errors.emailPessoal?.message}
          />
        )}
      />

      <Controller
        name="dataAgendamento"
        control={control}
        render={({ field }) => (
          <InputDateBox
            label="Datas disponíveis"
            availableDates={availableDates}
            disabled={isLoadingAvailableDates}
            selectedDate={field.value}
            onSelectDate={(date) => field.onChange(date)}
            error={!!errors.dataAgendamento}
            feedbackText={errors.dataAgendamento?.message}
          />
        )}
      />

      <Controller
        name="hour"
        control={control}
        render={({ field }) => (
          <SelectBox
            label="Selecione um horário"
            placeholder="Selecione um horário"
            options={availableHours}
            optionSelected={field.value}
            onSelect={(option) => field.onChange(option)}
            disabled={!selectedDate}
            error={!!errors.hour}
            feedbackText={errors.hour?.message}
          />
        )}
      />

      <Button
        type="submit"
        variant="secondary"
        color="white"
      >
        {isPending ? (
          <Spinner
            size={16}
            color="white"
          />
        ) : (
          'Enviar'
        )}
      </Button>
    </SC.Container>
  );
}
