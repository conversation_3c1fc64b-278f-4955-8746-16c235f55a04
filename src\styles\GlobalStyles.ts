import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        scroll-behavior: smooth;
    }

    body {
        max-width: 100vw;
        min-height: 100vh;
        overflow-x: hidden;
        background-color: ${({ theme }) => theme.colors.white};
        color: ${({ theme }) => theme.colors.black};
        font-family: ${({ theme }) => theme.fonts.primary};
    }

    a {
        color: inherit;
        text-decoration: none;
    }

    button {
        cursor: pointer;
    }

    a, button, p, h1, h2, h3, h4, h5, h6 {
        color: inherit;
    }

    /* Works on Firefox */
    * {
        scrollbar-width: thin;
    }
    /* Works on Chrome, Edge, and Safari */
    *::-webkit-scrollbar {
        width: 8px;
    }
`;
