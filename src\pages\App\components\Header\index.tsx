import { useAuth } from '@/contexts/auth';
import {
  // SearchInput,
  // SettingsLink,
  TriggerMenu,
  UserDropdown,
} from './components';
import ProfileChange from './components/ProfileChange';
import * as SC from './styles';
import { useEffect, useState } from 'react';

export default function Header() {
  const { user } = useAuth();
  const [hasToShowProfileChange, setHasToShowProfileChange] = useState(false);

  useEffect(() => {
    const isAdmin = user?.roles.some((role) => role.name.includes('Admin'));
    const hasMoreThanOneCompany = user?.companies.length > 1;
    const hasMoreThanOneRole = user?.roles.length > 1;

    if (isAdmin || hasMoreThanOneCompany || hasMoreThanOneRole) {
      setHasToShowProfileChange(true);
    } else {
      setHasToShowProfileChange(false);
    }
  }, [user]);

  return (
    <SC.Container>
      <SC.Box>
        <TriggerMenu />
        {hasToShowProfileChange && <ProfileChange />}
        {/* <SearchInput /> */}
      </SC.Box>
      <SC.UserBox>
        {/* <SettingsLink /> */}
        <UserDropdown />
      </SC.UserBox>
    </SC.Container>
  );
}
