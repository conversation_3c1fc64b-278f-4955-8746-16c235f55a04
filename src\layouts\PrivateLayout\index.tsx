import { Navigate, Outlet, useMatch } from 'react-router-dom';
import { <PERSON><PERSON>, MenuSideBar } from '@/pages/App/components';
import { useAuth } from '@/contexts/auth';
import * as SC from './styles';

export default function PrivateLayout() {
  const { user } = useAuth();

  const matchAppRoute = useMatch({ path: '/app', end: true });

  if (!user) {
    return (
      <Navigate
        to="/logout"
        replace
      />
    );
  }

  if (matchAppRoute) {
    return (
      <Navigate
        to="/app/home"
        replace
      />
    );
  }

  return (
    <SC.Container>
      <MenuSideBar />
      <SC.Content>
        <Header />
        <SC.RouteWrapper>
          <Outlet />
        </SC.RouteWrapper>
      </SC.Content>
    </SC.Container>
  );
}
