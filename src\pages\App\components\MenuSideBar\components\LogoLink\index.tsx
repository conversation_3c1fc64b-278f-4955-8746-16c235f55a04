import { useAuth } from '@/contexts/auth';
import { useCompany } from '@/contexts/company';
import { Link } from 'react-router-dom';
import * as SC from './styles';

export default function LogoLink() {
  const { company } = useCompany();
  const { user } = useAuth();

  const hasLogo = company?.logo && user.currentRole.requiresCompany;
  const companyLogo = company?.logo as string;

  return (
    <SC.Container>
      <Link to="/app/home">
        <img
          src={
            hasLogo
              ? companyLogo[0] === '/'
                ? `${import.meta.env.VITE_API_URL}/${company?.logo}`
                : company.logo ?? ''
              : '/imgs/bencorp-logo.png'
          }
          alt="logo"
        />
      </Link>
    </SC.Container>
  );
}
